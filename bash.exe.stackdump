Stack trace:
Frame         Function      Args
0007FFFFBF80  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBF80, 0007FFFFAE80) msys-2.0.dll+0x1FE8E
0007FFFFBF80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC258) msys-2.0.dll+0x67F9
0007FFFFBF80  000210046832 (000210286019, 0007FFFFBE38, 0007FFFFBF80, 000000000000) msys-2.0.dll+0x6832
0007FFFFBF80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBF80  000210068E24 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC260  00021006A225 (0007FFFFBF90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBA21E0000 ntdll.dll
7FFBA0D80000 KERNEL32.DLL
7FFB9F610000 KERNELBASE.dll
7FFBA1430000 USER32.dll
7FFB9F330000 win32u.dll
7FFBA16A0000 GDI32.dll
7FFB9FB80000 gdi32full.dll
7FFB9FD80000 msvcp_win.dll
7FFB9FE30000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFBA20C0000 advapi32.dll
7FFB9FF80000 msvcrt.dll
7FFBA0030000 sechost.dll
7FFBA1F90000 RPCRT4.dll
7FFB9E840000 CRYPTBASE.DLL
7FFB9F4E0000 bcryptPrimitives.dll
7FFBA13F0000 IMM32.DLL
